<?php

namespace app\modules\backend\controllers;

use app\common\models\ActionLog;
use app\common\models\Currency;
use app\common\models\Employee;
use app\common\models\EmployeeDebt;
use app\common\models\EmployeeDebtRepayment;
use Yii;
use yii\data\ArrayDataProvider;
use yii\web\Response;

class EmployeeDebtController extends BaseController
{
    /**
     * Список всех долгов
     */
    public function actionIndex()
    {
        $debts = EmployeeDebt::find()
            ->with(['employee', 'currency', 'createdBy'])
            ->where(['deleted_at' => null])
            ->orderBy(['created_at' => SORT_DESC])
            ->all();

        $dataProvider = new ArrayDataProvider([
            'allModels' => $debts,
            'pagination' => [
                'pageSize' => 50,
            ],
            'sort' => [
                'attributes' => ['created_at', 'amount', 'status'],
            ],
        ]);

        return $this->render('index', [
            'dataProvider' => $dataProvider
        ]);
    }

    /**
     * Поиск долгов с фильтрацией
     */
    public function actionSearch()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $employeeId = Yii::$app->request->post('employee_id');
        $status = Yii::$app->request->post('status');
        $startDate = Yii::$app->request->post('start_date');
        $endDate = Yii::$app->request->post('end_date');

        $sql = "SELECT
            ed.id,
            ed.employee_id,
            e.full_name as employee_name,
            p.name as position,
            ed.amount,
            ed.remaining_amount,
            ed.description,
            ed.status,
            c.name as currency_name,
            u.username as created_by_name,
            ed.created_at,
            CASE
                WHEN ed.status = 1 THEN 'Активный'
                WHEN ed.status = 2 THEN 'Частично погашен'
                WHEN ed.status = 3 THEN 'Полностью погашен'
                WHEN ed.status = 4 THEN 'Отменен'
                ELSE 'Неизвестно'
            END as status_name
        FROM employee_debts ed
        LEFT JOIN employee e ON e.id = ed.employee_id
        LEFT JOIN position p ON p.id = e.position_id
        LEFT JOIN currency c ON c.id = ed.currency_id
        LEFT JOIN users u ON u.id = ed.created_by
        WHERE ed.deleted_at IS NULL";

        $params = [];

        if ($employeeId) {
            $sql .= " AND ed.employee_id = :employee_id";
            $params[':employee_id'] = $employeeId;
        }

        if ($status !== '' && $status !== null) {
            $sql .= " AND ed.status = :status";
            $params[':status'] = $status;
        }

        if ($startDate) {
            $sql .= " AND DATE(ed.created_at) >= :start_date";
            $params[':start_date'] = $startDate;
        }

        if ($endDate) {
            $sql .= " AND DATE(ed.created_at) <= :end_date";
            $params[':end_date'] = $endDate;
        }

        $sql .= " ORDER BY ed.created_at DESC";

        try {
            $command = Yii::$app->db->createCommand($sql);
            foreach ($params as $key => $value) {
                $command->bindValue($key, $value);
            }
            $result = $command->queryAll();

            return [
                'status' => 'success',
                'html' => $this->renderPartial('_index', [
                    'result' => $result
                ])
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Создание нового долга
     */
    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $model = new EmployeeDebt();

            $employees = Employee::find()
                ->select(['id', 'full_name'])
                ->where(['deleted_at' => null])
                ->asArray()
                ->all();

            // Временная отладочная информация
            Yii::error('Employees found in actionCreate: ' . json_encode($employees), 'debug');

            $currencies = Currency::find()
                ->where(['deleted_at' => null])
                ->all();

            return [
                'status' => 'success',
                'content' => $this->renderPartial('create', [
                    'model' => $model,
                    'employees' => $employees,
                    'currencies' => $currencies
                ])
            ];
        }

        if (!Yii::$app->request->isPost) {
            return [
                'status' => 'error',
                'message' => 'Неверный метод запроса',
            ];
        }

        $model = new EmployeeDebt();
        $model->load(Yii::$app->request->post());

        if (!$model->validate()) {
            return [
                'status' => 'error',
                'errors' => $model->getErrors(),
            ];
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$model->save()) {
                throw new \Exception('Не удалось сохранить долг: ' . json_encode($model->getErrors()));
            }

            // Логируем создание долга
            ActionLog::logAction(
                Yii::$app->user->id,
                ActionLog::TYPE_DEBT_MANAGEMENT,
                null,
                json_encode([
                    'action' => 'create',
                    'debt_id' => $model->id,
                    'employee_id' => $model->employee_id,
                    'amount' => $model->amount,
                    'currency_id' => $model->currency_id,
                    'description' => $model->description,
                    'created_at' => date('Y-m-d H:i:s')
                ])
            );

            $transaction->commit();
            return [
                'status' => 'success',
                'message' => 'Долг успешно создан',
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Обновление долга
     */
    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = EmployeeDebt::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => 'Долг не найден',
                ];
            }

            $employees = Employee::find()
                ->select(['id', 'full_name'])
                ->where(['deleted_at' => null])
                ->asArray()
                ->all();

            $currencies = Currency::find()
                ->where(['deleted_at' => null])
                ->all();

            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', [
                    'model' => $model,
                    'employees' => $employees,
                    'currencies' => $currencies
                ])
            ];
        }

        if (!Yii::$app->request->isPost) {
            return [
                'status' => 'error',
                'message' => 'Неверный метод запроса',
            ];
        }

        $data = Yii::$app->request->post();
        $model = EmployeeDebt::findOne($data['EmployeeDebt']['id']);

        if (!$model) {
            return [
                'status' => 'error',
                'message' => 'Долг не найден',
            ];
        }

        $model->load(Yii::$app->request->post());

        if (!$model->validate()) {
            return [
                'status' => 'error',
                'errors' => $model->getErrors(),
            ];
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$model->save()) {
                throw new \Exception('Не удалось обновить долг: ' . json_encode($model->getErrors()));
            }

            // Логируем обновление долга
            ActionLog::logAction(
                Yii::$app->user->id,
                ActionLog::TYPE_DEBT_MANAGEMENT,
                null,
                json_encode([
                    'action' => 'update',
                    'debt_id' => $model->id,
                    'employee_id' => $model->employee_id,
                    'amount' => $model->amount,
                    'currency_id' => $model->currency_id,
                    'description' => $model->description,
                    'updated_at' => date('Y-m-d H:i:s')
                ])
            );

            $transaction->commit();
            return [
                'status' => 'success',
                'message' => 'Долг успешно обновлен',
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Просмотр деталей долга
     */
    public function actionView($id)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $debt = EmployeeDebt::find()
            ->with(['employee', 'currency', 'createdBy', 'repayments'])
            ->where(['id' => $id, 'deleted_at' => null])
            ->one();

        if (!$debt) {
            return [
                'status' => 'error',
                'message' => 'Долг не найден',
            ];
        }

        return [
            'status' => 'success',
            'content' => $this->renderPartial('view', [
                'debt' => $debt
            ])
        ];
    }

    /**
     * Получить активные долги сотрудника для AJAX
     */
    public function actionGetActiveDebts()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        $employeeId = Yii::$app->request->get('employee_id');
        $currencyId = Yii::$app->request->get('currency_id');

        if (!$employeeId) {
            return [
                'status' => 'error',
                'message' => 'Сотрудник не указан',
            ];
        }

        try {
            // Используем новый метод с конвертацией валют
            $debtsData = EmployeeDebt::getActiveDebtsWithConversion($employeeId, $currencyId);

            $totalDebt = 0;
            $totalConvertedDebt = 0;

            // Подготавливаем данные для фронтенда
            foreach ($debtsData as &$debt) {
                $debt['created_at_formatted'] = date('d.m.Y', strtotime($debt['created_at']));
                $debt['amount_formatted'] = number_format($debt['amount'], 2, '.', ' ');
                $debt['remaining_amount_formatted'] = number_format($debt['remaining_amount'], 2, '.', ' ');

                if (isset($debt['converted_amount'])) {
                    $debt['converted_amount_formatted'] = number_format($debt['converted_amount'], 2, '.', ' ');
                    $totalConvertedDebt += $debt['converted_amount'];
                } else {
                    $debt['converted_amount_formatted'] = $debt['remaining_amount_formatted'];
                    $totalConvertedDebt += $debt['remaining_amount'];
                }

                $totalDebt += $debt['remaining_amount'];
            }

            // Получаем информацию о целевой валюте
            $targetCurrency = null;
            if ($currencyId) {
                $targetCurrency = \app\common\models\Currency::findOne($currencyId);
            }

            return [
                'status' => 'success',
                'debts' => $debtsData,
                'total_debt' => $totalDebt,
                'total_converted_debt' => $totalConvertedDebt,
                'total_debt_formatted' => number_format($totalDebt, 2, '.', ' '),
                'total_converted_debt_formatted' => number_format($totalConvertedDebt, 2, '.', ' '),
                'target_currency' => $targetCurrency ? [
                    'id' => $targetCurrency->id,
                    'name' => $targetCurrency->name,
                    'code' => $targetCurrency->code ?? ''
                ] : null,
                'has_debts' => count($debtsData) > 0,
                'debts_count' => count($debtsData)
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Удаление долга (мягкое удаление)
     */
    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $debt = EmployeeDebt::findOne($id);

            if (!$debt) {
                return [
                    'status' => 'error',
                    'message' => 'Долг не найден',
                ];
            }

            return [
                'status' => 'success',
                'content' => $this->renderPartial('delete', ['debt' => $debt]),
            ];
        }

        if (!Yii::$app->request->isPost) {
            return [
                'status' => 'error',
                'message' => 'Неверный метод запроса',
            ];
        }

        $data = Yii::$app->request->post();
        $debt = EmployeeDebt::findOne($data['EmployeeDebt']['id']);

        if (!$debt) {
            return [
                'status' => 'error',
                'message' => 'Долг не найден',
            ];
        }

        // Проверяем, есть ли погашения
        if ($debt->getTotalRepaid() > 0) {
            return [
                'status' => 'error',
                'message' => 'Нельзя удалить долг с погашениями. Сначала отмените связанные выплаты.',
            ];
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $debt->deleted_at = date('Y-m-d H:i:s');
            if (!$debt->save()) {
                throw new \Exception('Ошибка при удалении долга');
            }

            // Логируем удаление долга
            ActionLog::logAction(
                Yii::$app->user->id,
                ActionLog::TYPE_DEBT_MANAGEMENT,
                json_encode([
                    'action' => 'delete',
                    'debt_id' => $debt->id,
                    'employee_id' => $debt->employee_id,
                    'amount' => $debt->amount,
                    'remaining_amount' => $debt->remaining_amount,
                    'description' => $debt->description
                ]),
                null
            );

            $transaction->commit();
            return [
                'status' => 'success',
                'message' => 'Долг успешно удален',
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
            ];
        }
    }
}
