<?php

namespace app\common\models;

use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "employee_debts".
 *
 * @property int $id
 * @property int $employee_id
 * @property float $amount
 * @property int $currency_id
 * @property float $remaining_amount
 * @property string|null $description
 * @property int|null $status
 * @property int $created_by
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 *
 * @property Employee $employee
 * @property Currency $currency
 * @property User $createdBy
 * @property EmployeeDebtRepayment[] $repayments
 */
class EmployeeDebt extends ActiveRecord
{
    // Статусы долга
    const STATUS_ACTIVE = 1;           // Активный
    const STATUS_PARTIALLY_PAID = 2;   // Частично погашен  
    const STATUS_FULLY_PAID = 3;       // Полностью погашен
    const STATUS_CANCELLED = 4;        // Отменен

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'employee_debts';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['employee_id', 'amount', 'currency_id', 'description'], 'required'],
            [['employee_id', 'currency_id', 'status', 'created_by'], 'integer'],
            [['amount', 'remaining_amount'], 'number', 'min' => 0.01],
            [['description'], 'string', 'min' => 10, 'max' => 1000],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['status'], 'in', 'range' => [self::STATUS_ACTIVE, self::STATUS_PARTIALLY_PAID, self::STATUS_FULLY_PAID, self::STATUS_CANCELLED]],
            [['employee_id'], 'exist', 'skipOnError' => true, 'targetClass' => Employee::class, 'targetAttribute' => ['employee_id' => 'id']],
            [['currency_id'], 'exist', 'skipOnError' => true, 'targetClass' => Currency::class, 'targetAttribute' => ['currency_id' => 'id']],
            [['created_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['created_by' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'employee_id' => Yii::t('app', 'employee'),
            'amount' => Yii::t('app', 'amount'),
            'currency_id' => Yii::t('app', 'currency'),
            'remaining_amount' => 'Остаток долга',
            'description' => Yii::t('app', 'description'),
            'status' => 'Статус',
            'created_by' => 'Создал',
            'created_at' => Yii::t('app', 'created_at'),
            'updated_at' => 'Обновлен',
            'deleted_at' => Yii::t('app', 'deleted_at'),
        ];
    }

    /**
     * Получить статусы долгов
     */
    public static function getStatuses()
    {
        return [
            self::STATUS_ACTIVE => 'Активный',
            self::STATUS_PARTIALLY_PAID => 'Частично погашен',
            self::STATUS_FULLY_PAID => 'Полностью погашен',
            self::STATUS_CANCELLED => 'Отменен',
        ];
    }

    /**
     * Получить название статуса
     */
    public function getStatusName()
    {
        $statuses = self::getStatuses();
        return $statuses[$this->status] ?? 'Неизвестно';
    }

    /**
     * {@inheritdoc}
     */
    public function beforeSave($insert)
    {
        if (parent::beforeSave($insert)) {
            if ($insert) {
                $this->remaining_amount = $this->amount;
                $this->status = self::STATUS_ACTIVE;
                $this->created_by = Yii::$app->user->id;
                $this->created_at = date('Y-m-d H:i:s');
            }
            $this->updated_at = date('Y-m-d H:i:s');
            return true;
        }
        return false;
    }

    /**
     * Gets query for [[Employee]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getEmployee()
    {
        return $this->hasOne(Employee::class, ['id' => 'employee_id']);
    }

    /**
     * Gets query for [[Currency]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCurrency()
    {
        return $this->hasOne(Currency::class, ['id' => 'currency_id']);
    }

    /**
     * Gets query for [[CreatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCreatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    /**
     * Gets query for [[Repayments]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getRepayments()
    {
        return $this->hasMany(EmployeeDebtRepayment::class, ['debt_id' => 'id'])
            ->where(['deleted_at' => null])
            ->orderBy(['created_at' => SORT_ASC]);
    }

    /**
     * Получить общую сумму погашений
     */
    public function getTotalRepaid()
    {
        return $this->getRepayments()->sum('amount') ?: 0;
    }

    /**
     * Обновить статус долга на основе остатка
     */
    public function updateStatus()
    {
        if ($this->remaining_amount <= 0) {
            $this->status = self::STATUS_FULLY_PAID;
        } elseif ($this->remaining_amount < $this->amount) {
            $this->status = self::STATUS_PARTIALLY_PAID;
        } else {
            $this->status = self::STATUS_ACTIVE;
        }
        
        $this->updated_at = date('Y-m-d H:i:s');
        return $this->save(false);
    }

    /**
     * Получить активные долги сотрудника
     */
    public static function getActiveDebts($employeeId, $currencyId = null)
    {
        $query = self::find()
            ->with(['currency']) // Загружаем связанную валюту
            ->where([
                'employee_id' => $employeeId,
                'deleted_at' => null
            ])
            ->andWhere(['in', 'status', [self::STATUS_ACTIVE, self::STATUS_PARTIALLY_PAID]])
            ->andWhere(['>', 'remaining_amount', 0])
            ->orderBy(['created_at' => SORT_ASC]);

        // Убираем фильтрацию по валюте - показываем ВСЕ долги
        // if ($currencyId) {
        //     $query->andWhere(['currency_id' => $currencyId]);
        // }

        return $query->all();
    }

    /**
     * Получить активные долги сотрудника с конвертацией в указанную валюту
     */
    public static function getActiveDebtsWithConversion($employeeId, $targetCurrencyId = null)
    {
        $debts = self::getActiveDebts($employeeId);
        $result = [];

        foreach ($debts as $debt) {
            $debtData = [
                'id' => $debt->id,
                'amount' => $debt->amount,
                'remaining_amount' => $debt->remaining_amount,
                'description' => $debt->description,
                'currency_id' => $debt->currency_id,
                'currency_name' => $debt->currency->name ?? '',
                'currency_code' => $debt->currency->code ?? '',
                'created_at' => $debt->created_at,
                'original_amount' => $debt->remaining_amount,
                'original_currency' => $debt->currency->name ?? '',
            ];

            // Если указана целевая валюта и она отличается от валюты долга
            if ($targetCurrencyId && $debt->currency_id != $targetCurrencyId) {
                $convertedAmount = self::convertCurrency(
                    $debt->remaining_amount,
                    $debt->currency_id,
                    $targetCurrencyId
                );
                $debtData['converted_amount'] = $convertedAmount;
                $debtData['target_currency_id'] = $targetCurrencyId;
            } else {
                $debtData['converted_amount'] = $debt->remaining_amount;
                $debtData['target_currency_id'] = $debt->currency_id;
            }

            $result[] = $debtData;
        }

        return $result;
    }

    /**
     * Конвертировать валюту
     */
    public static function convertCurrency($amount, $fromCurrencyId, $toCurrencyId)
    {
        if ($fromCurrencyId == $toCurrencyId) {
            return $amount;
        }

        // Получаем текущие курсы валют
        $fromRate = self::getCurrentCurrencyRate($fromCurrencyId);
        $toRate = self::getCurrentCurrencyRate($toCurrencyId);

        if (!$fromRate || !$toRate) {
            return $amount; // Если курс не найден, возвращаем исходную сумму
        }

        // Конвертируем через доллары (базовая валюта)
        // Если валюта - доллар, курс = 1
        $usdAmount = ($fromRate == 1) ? $amount : $amount / $fromRate;
        $convertedAmount = ($toRate == 1) ? $usdAmount : $usdAmount * $toRate;

        return round($convertedAmount, 2);
    }

    /**
     * Получить текущий курс валюты
     */
    public static function getCurrentCurrencyRate($currencyId)
    {
        // Проверяем, является ли валюта долларом (основная валюта)
        $currency = \app\common\models\Currency::findOne($currencyId);
        if ($currency && $currency->is_main) {
            return 1; // Доллар имеет курс 1
        }

        $course = \app\common\models\CurrencyCourse::find()
            ->where(['currency_id' => $currencyId])
            ->andWhere(['<=', 'start_date', date('Y-m-d')])
            ->andWhere(['>', 'end_date', date('Y-m-d')])
            ->andWhere(['deleted_at' => null])
            ->orderBy(['start_date' => SORT_DESC])
            ->one();

        return $course ? (float)$course->course : 1;
    }

    /**
     * Проверить возможность погашения суммы
     */
    public function canRepay($amount)
    {
        return $amount > 0 && $amount <= $this->remaining_amount;
    }

    /**
     * Погасить часть долга
     */
    public function repay($amount, $employeeFinanceId, $description = null)
    {
        if (!$this->canRepay($amount)) {
            throw new \Exception('Сумма погашения превышает остаток долга');
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            // Создаем запись о погашении
            $repayment = new EmployeeDebtRepayment();
            $repayment->debt_id = $this->id;
            $repayment->employee_finance_id = $employeeFinanceId;
            $repayment->amount = $amount;
            $repayment->currency_id = $this->currency_id;
            $repayment->description = $description ?: 'Погашение долга';
            $repayment->created_by = Yii::$app->user->id;

            if (!$repayment->save()) {
                throw new \Exception('Не удалось создать запись о погашении: ' . json_encode($repayment->getErrors()));
            }

            // Обновляем остаток долга
            $this->remaining_amount -= $amount;
            $this->updateStatus();

            $transaction->commit();
            return $repayment;
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }
}
