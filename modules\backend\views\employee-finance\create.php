<?php

use app\common\models\EmployeeFinances;
use yii\helpers\Html;

$type_payments = EmployeeFinances::getTypes();

?>

<div class="employee-finance-form">
    <form id="employee-finance-create-form">
        <div class="form-group">
            <label for="employee_id"><?= Yii::t('app', 'employee') ?></label>
            <select id="employee_id" name="EmployeeFinances[employee_id]" class="form-control select2" required>
                <option value="">Ходимни танланг</option>
                <?php foreach ($workers as $worker): ?>
                    <option value="<?= $worker['id'] ?>"><?= Html::encode($worker['full_name']) ?></option>
                <?php endforeach; ?>
            </select>
            <div class="error-container" id="employee_id-error"></div>
        </div>

        <div class="form-group">
            <label for="type_payment"><?= Yii::t('app', 'type_payment') ?></label>
            <select id="type_payment" name="EmployeeFinances[type]" class="form-control select2" required>
                <option value="">Туринни танланг</option>
                <?php foreach ($type_payments as $value => $label): ?>
                    <option value="<?= $value ?>"><?= $label ?></option>
                <?php endforeach; ?>
            </select>
            <div class="error-container" id="type_payment-error"></div>
        </div>

        <div class="form-group">
            <label for="month"><?= Yii::t('app', 'month') ?></label>
            <input type="month" id="month" name="EmployeeFinances[month]" class="form-control" required readonly>
            <div class="error-container" id="month-error"></div>
        </div>

        <div class="form-group">
            <label for="amount"><?= Yii::t('app', 'amount') ?></label>
            <input type="number" id="amount" name="EmployeeFinances[amount]" class="form-control" required min="0" step="0.01">
            <small class="form-text text-muted" id="amount_hint"></small>
            <div class="error-container" id="amount-error"></div>
        </div>

        <div class="form-group">
            <label for="cashbox_currency"><?= Yii::t('app', 'cashbox') ?></label>
            <select id="cashbox_currency" class="form-control select2" required>
                <option value="">Кассани танланг</option>
                <?php foreach ($cashboxData as $cashbox): ?>
                    <optgroup label="<?= Html::encode($cashbox['name']) ?>">
                        <?php foreach ($cashbox['balances'] as $balance): ?>
                            <option value="<?= $cashbox['id'] ?>_<?= $balance['currency_id'] ?>"
                                    data-cashbox="<?= $cashbox['id'] ?>"
                                    data-currency="<?= $balance['currency_id'] ?>"
                                    data-balance="<?= $balance['sum'] ?>">
                                <?= Html::encode($balance['currency_name']) ?> (<?= number_format($balance['sum'], 0, '.', ' ') ?> сўм)
                            </option>
                        <?php endforeach; ?>
                    </optgroup>
                <?php endforeach; ?>
            </select>
            <div class="error-container" id="cashbox_id-error"></div>
        </div>

        <input type="hidden" id="cashbox_id" name="EmployeeFinances[cashbox_id]">
        <input type="hidden" id="currency_id" name="EmployeeFinances[currency_id]">

        <!-- Секция управления долгами -->
        <div id="debt-section" class="form-group" style="display: none;">
            <div class="form-check">
                <input type="checkbox" class="form-check-input" id="deduct_debt" name="deduct_debt" value="1">
                <label class="form-check-label" for="deduct_debt">
                    <strong><?= Yii::t('app', 'deduct_debts_from_salary') ?></strong>
                    <span id="debt-info" class="text-muted"></span>
                </label>
            </div>

            <div id="deduction-input" class="mt-3" style="display: none;">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="deduction_amount"><?= Yii::t('app', 'deduction_amount') ?></label>
                            <div class="input-group">
                                <input type="number" id="deduction_amount" name="deduction_amount"
                                       class="form-control" min="0" step="0.01" placeholder="0.00">
                                <div class="input-group-append">
                                    <span class="input-group-text" id="deduction-currency"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Итоговая сумма к выплате -->
        <div id="payment-summary" class="form-group">
            <div class="card border-success">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong><?= Yii::t('app', 'gross_amount') ?>:</strong>
                            <span id="gross-amount">0</span>
                        </div>
                        <div class="col-md-4">
                            <strong><?= Yii::t('app', 'debt_deduction') ?>:</strong>
                            <span id="debt-deduction-display">0</span>
                        </div>
                        <div class="col-md-4">
                            <strong class="text-success"><?= Yii::t('app', 'net_amount') ?>:</strong>
                            <span id="net-amount" class="text-success">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="description"><?= Yii::t('app', 'description') ?></label>
            <textarea id="description" name="EmployeeFinances[description]" class="form-control"></textarea>
            <div class="error-container" id="description-error"></div>
        </div>
    </form>
</div>

<style>
    /* Стили для итоговой суммы */
    .payment-summary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid rgba(255,255,255,0.2);
    }

    .summary-item:last-child {
        border-bottom: none;
        font-weight: bold;
        font-size: 1.1em;
    }

    .summary-label {
        font-weight: 500;
    }

    .summary-value {
        font-weight: bold;
    }

    /* Простые стили для секции долгов */
    #debt-info {
        font-size: 0.9em;
    }
</style>

<script>
$(document).ready(function() {

    $('.select2').select2();

    // Сохраняем оригинальные опции
    var originalOptions = {};
    $('.select2').each(function() {
        var id = $(this).attr('id');
        originalOptions[id] = $(this).find('option').clone();
    });
    // Функция для получения месяца и суммы
    function getMonthAndAmount(employeeId, type) {
        $.ajax({
            url: '/backend/employee-finance/get-last-payment-month',
            type: 'GET',
            data: {
                employee_id: employeeId,
                type: type
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    $('#month').val(response.month);
                    if (response.remaining > 0) {
                        $('#amount_hint').text('Шу ой учун қолган маош: ' + response.remaining.toFixed(2) + ' сўм');
                    } else {
                        $('#amount_hint').text('');
                    }
                }
            }
        });
    }

    $('#employee_id').on('change', function() {
        var employeeId = $(this).val();
        var $select = $('#type_payment');

        if (employeeId) {
            $.ajax({
                url: '/backend/employee-finance/check-salary',
                type: 'GET',
                data: { employee_id: employeeId },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        if (response.has_salary) {
                            // Если зарплата назначена
                            $select.empty().append(originalOptions['type_payment'].clone());
                            $select.val('1').trigger('change');
                            $('#month').prop('readonly', true);
                            getMonthAndAmount(employeeId, 1);
                        } else {
                            // Если зарплата не назначена
                            var emptyOption = originalOptions['type_payment'].filter('[value=""]').clone();
                            var bonusOption = originalOptions['type_payment'].filter('[value="3"]').clone();
                            $select.empty()
                                  .append(emptyOption)
                                  .append(bonusOption)
                                  .val('3')
                                  .trigger('change');
                            $('#month').prop('readonly', false);
                            $('#month').val('');
                            $('#amount').attr('placeholder', 'Введите сумму');
                            $('#amount').val('');
                        }
                    }
                }
            });
        } else {
            // Если работник не выбран
            $select.empty()
                  .append(originalOptions['type_payment'].clone())
                  .val('')
                  .trigger('change');
            $('#month').val('');
            $('#month').prop('readonly', true);
            $('#amount').attr('placeholder', 'Қолган сумма: 0');
            $('#amount').val('');
        }
    });

    // При изменении типа оплаты
    $('#type_payment').on('change', function() {
        var employeeId = $('#employee_id').val();
        var type = $(this).val();

        if (employeeId && type) {
            if (type == 3) { // Если выбран бонус
                $('#month').prop('readonly', false); // Разблокируем выбор месяца
                $('#month').val(''); // Очищаем месяц
                $('#amount_hint').text(''); // Очищаем рекомендованную сумму
                $('#amount').val(''); // Очищаем сумму
            } else { // Если выбрана зарплата или аванс
                $('#month').prop('readonly', true); // Блокируем выбор месяца
                getMonthAndAmount(employeeId, type); // Получаем месяц и сумму
            }
        }
    });

    // При изменении кассы и валюты
    $('#cashbox_currency').on('change', function() {
        var $selected = $(this).find('option:selected');
        if ($selected.val()) {
            // Устанавливаем значения в скрытые поля
            $('#cashbox_id').val($selected.data('cashbox'));
            $('#currency_id').val($selected.data('currency'));

            // Загружаем долги сотрудника в выбранной валюте
            loadEmployeeDebts();
        } else {
            $('#cashbox_id').val('');
            $('#currency_id').val('');
            hideDebtSection();
        }
        updatePaymentSummary();
    });

    // При изменении суммы выплаты
    $('#amount').on('input', function() {
        updatePaymentSummary();
    });

    // При изменении checkbox удержания долгов
    $('#deduct_debt').on('change', function() {
        if ($(this).is(':checked')) {
            $('#deduction-input').show();
            var totalDebt = parseFloat($('#deduction_amount').attr('data-total-debt')) || 0;
            $('#deduction_amount').val(totalDebt.toFixed(2));
        } else {
            $('#deduction-input').hide();
            $('#deduction_amount').val('0');
        }
        updatePaymentSummary();
    });

    // При изменении суммы удержания
    $('#deduction_amount').on('input', function() {
        var deductionAmount = parseFloat($(this).val()) || 0;
        var maxDebt = parseFloat($(this).attr('data-total-debt')) || 0;

        if (deductionAmount > maxDebt) {
            $(this).val(maxDebt.toFixed(2));
            alert('Сумма удержания не может превышать общую сумму долгов');
        }
        updatePaymentSummary();
    });

    // Функция загрузки долгов сотрудника
    function loadEmployeeDebts() {
        var employeeId = $('#employee_id').val();
        var currencyId = $('#currency_id').val();

        if (!employeeId) {
            hideDebtSection();
            return;
        }

        $.ajax({
            url: '/backend/employee-debt/get-active-debts',
            type: 'GET',
            data: {
                employee_id: employeeId,
                currency_id: currencyId || null
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success' && response.has_debts) {
                    showDebtSection(response);
                } else {
                    hideDebtSection();
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading debts:', error);
                hideDebtSection();
            }
        });
    }

    // Функция отображения секции долгов
    function showDebtSection(debtData) {
        // Показываем секцию долгов
        $('#debt-section').show();

        // Формируем информацию о долгах
        var debtInfo = '';
        if (debtData.debts_count > 0) {
            // Собираем уникальные валюты
            var uniqueCurrencies = [];
            var totalByCurrency = {};

            $.each(debtData.debts, function(index, debt) {
                if (uniqueCurrencies.indexOf(debt.currency_name) === -1) {
                    uniqueCurrencies.push(debt.currency_name);
                    totalByCurrency[debt.currency_name] = 0;
                }
                totalByCurrency[debt.currency_name] += debt.remaining_amount;
            });

            // Формируем строку с суммами по валютам
            var currencyStrings = [];
            $.each(uniqueCurrencies, function(index, currency) {
                currencyStrings.push(totalByCurrency[currency].toFixed(2) + ' ' + currency);
            });

            debtInfo = '(Общий долг: ' + currencyStrings.join(', ') + ')';
        }

        $('#debt-info').text(debtInfo);

        // Устанавливаем валюту удержания и максимальную сумму
        if (debtData.target_currency) {
            $('#deduction-currency').text(debtData.target_currency.name);
        } else {
            $('#deduction-currency').text('');
        }

        $('#deduction_amount').attr('data-total-debt', debtData.total_converted_debt);
        $('#deduction_amount').attr('max', debtData.total_converted_debt);

        updatePaymentSummary();
    }

    // Функция скрытия секции долгов
    function hideDebtSection() {
        $('#debt-section').hide();
        $('#deduct_debt').prop('checked', false);
        $('#deduction-input').hide();
        $('#deduction_amount').val('0');
        updatePaymentSummary();
    }



    // Функция обновления итоговой суммы
    function updatePaymentSummary() {
        var grossAmount = parseFloat($('#amount').val()) || 0;
        var deductionAmount = $('#deduct_debt').is(':checked') ? (parseFloat($('#deduction_amount').val()) || 0) : 0;
        var netAmount = grossAmount - deductionAmount;

        // Получаем название валюты
        var currencyName = $('#cashbox_currency option:selected').text().split('(')[0].trim() || '';

        $('#gross-amount').text(grossAmount.toFixed(2) + ' ' + currencyName);
        $('#debt-deduction-display').text(deductionAmount.toFixed(2) + ' ' + currencyName);
        $('#net-amount').text(netAmount.toFixed(2) + ' ' + currencyName);
    }

    // Загружаем долги при изменении сотрудника
    $('#employee_id').on('change', function() {
        loadEmployeeDebts();
    });
});
</script>